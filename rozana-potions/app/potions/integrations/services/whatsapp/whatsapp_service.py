import os
import httpx
from django.conf import settings
from django.db import connections
from typing import Optional, Dict, Any

from potions.logging.utils import get_app_logger

logger = get_app_logger('whatsapp_service')


class CustomerData:
    """Simple data class to hold customer information"""
    def __init__(self, full_name: str, phone_number: str, amount: float, delivery_time: int = 24):
        self.full_name = full_name
        self.phone_number = phone_number
        self.amount = amount
        self.delivery_time = delivery_time


class WhatsAppService:
    """WhatsApp messaging service using Gupshup API"""
    
    def __init__(self):
        self.url = getattr(settings, 'GUPSHUP_URL', os.getenv('GUPSHUP_URL'))
        self.userid = getattr(settings, 'GUPSHUP_USERID', os.getenv('GUPSHUP_USERID'))
        self.password = getattr(settings, 'GUPSHUP_PASSWORD', os.getenv('GUPSHUP_PASSWORD'))
        self.timeout = getattr(settings, 'GUPSHUP_TIMEOUT', float(os.getenv('GUPSHUP_TIMEOUT', '30.0')))
        self.enabled = getattr(settings, 'WHATSAPP_ENABLED', str(os.getenv('WHATSAPP_ENABLED', 'false')).lower() == 'true')

    def send_message(self, phone: str, message: str) -> bool:
        """Send a WhatsApp message via Gupshup API"""
        if not self.enabled:
            logger.info(f"WhatsApp is disabled, skipping message to {phone}")
            return True  # Return True to avoid breaking flows when disabled
            
        params = {
            "userid": self.userid,
            "password": self.password,
            "method": "SENDMESSAGE",
            "send_to": phone,
            "msg": message,
            "msg_type": "TEXT",
            "format": "json",
            "v": "1.1",
        }

        try:
            with httpx.Client(timeout=self.timeout) as client:
                response = client.get(self.url, params=params)

            if response.status_code == 200:
                try:
                    result = response.json()
                except Exception:
                    logger.error("Failed to parse WhatsApp API response as JSON", exc_info=True)
                    return False

                if result.get("response", {}).get("status") == "success":
                    logger.info(f"WhatsApp message sent successfully to {phone}")
                    return True
                else:
                    logger.error(f"WhatsApp API error: {result}")
                    return False
            else:
                logger.error(f"WhatsApp HTTP error: {response.status_code} - {response.text}")
                return False

        except Exception as e:
            logger.error(f"Exception sending WhatsApp message: {str(e)}", exc_info=True)
            return False
