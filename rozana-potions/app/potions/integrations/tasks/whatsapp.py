from celery import shared_task
from typing import Dict, Any, Optional
from integrations.services.whatsapp.whatsapp_service import WhatsAppService, CustomerData
from integrations.services.whatsapp.message_templates import WhatsAppMessageTemplates
from potions.logging.utils import get_app_logger

logger = get_app_logger('whatsapp_tasks')


@shared_task(bind=True, max_retries=3, default_retry_delay=30)
def send_whatsapp_event_notification_task(
    self, 
    event_type: str, 
    payload: Dict[str, Any], 
    metadata: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Celery task to send WhatsApp notifications based on event type
    
    Args:
        event_type: Type of event (e.g., 'order_created', 'order_delivered')
        payload: Event payload containing customer and order information
        metadata: Optional metadata for tracking
        
    Returns:
        dict: Task result with success status and details
    """
    try:
        logger.info(f"Processing WhatsApp notification for event: {event_type}")
        
        whatsapp_service = WhatsAppService()
        
        # Extract order_id from payload
        order_id = payload.get('order_id')
        if not order_id:
            logger.error(f"No order_id found in payload for event {event_type}")
            return {
                'success': False,
                'event_type': event_type,
                'error': 'No order_id found in payload',
                'metadata': metadata or {}
            }
        
        address = payload.get('address', {})
        phone_number = address.get('phone_number')
        if not phone_number:
            logger.warning(f"No phone number found for event {event_type}")
            return {
                'success': False,
                'event_type': event_type,
                'error': 'No phone number found',
                'metadata': metadata or {}
            }

        # Build CustomerData object (templates expect attribute access)
        customer_data = CustomerData(
            full_name=address.get('full_name', 'Customer'),
            phone_number=phone_number,
            amount=payload.get('total_amount', 0.0),
            delivery_time=24  # Default delivery time in hours
        )

        # Generate message based on event type
        message = _generate_message_for_event(event_type, payload, customer_data)
        
        if not message:
            logger.error(f"Could not generate message for event type: {event_type}")
            return {
                'success': False,
                'event_type': event_type,
                'error': f'Unknown event type: {event_type}',
                'metadata': metadata or {}
            }
        
        # Send the message
        success = whatsapp_service.send_message(customer_data.phone_number, message)
        
        if success:
            logger.info(f"WhatsApp notification sent successfully for event: {event_type}")
            return {
                'success': True,
                'event_type': event_type,
                'phone': customer_data.phone_number,
                'message': 'WhatsApp notification sent successfully',
                'metadata': metadata or {}
            }
        else:
            logger.error(f"Failed to send WhatsApp notification for event: {event_type}")
            return {
                'success': False,
                'event_type': event_type,
                'phone': customer_data.phone_number,
                'error': 'Message sending failed',
                'metadata': metadata or {}
            }
            
    except Exception as exc:
        logger.error(f"Exception in send_whatsapp_event_notification_task for {event_type}: {str(exc)}")
        
        # Retry the task if retries are available
        if self.request.retries < self.max_retries:
            logger.info(f"Retrying WhatsApp notification for {event_type} (attempt {self.request.retries + 1})")
            raise self.retry(exc=exc)
            
        return {
            'success': False,
            'event_type': event_type,
            'error': str(exc),
            'message': 'Exception occurred while sending WhatsApp notification',
            'retries_exhausted': True,
            'metadata': metadata or {}
        }


def _generate_message_for_event(event_type: str, payload: Dict[str, Any], customer_data) -> Optional[str]:
    """Generate appropriate WhatsApp message based on event type"""
    order_id = payload.get('order_id', 'N/A')
    
    try:
        # Get the template method directly by name (e.g., order_created, order_delivered, etc.)
        template_fn = getattr(WhatsAppMessageTemplates, event_type)
        return template_fn(customer_data, order_id)
    except AttributeError:
        # Log unsupported events but don't fail
        logger.info(f"WhatsApp notifications not configured for event type: {event_type}")
        return None
