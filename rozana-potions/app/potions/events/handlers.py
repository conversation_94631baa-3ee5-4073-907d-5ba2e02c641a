from potions.logging.utils import get_app_logger
from django.conf import settings
from integrations.tasks.whatsapp import send_whatsapp_event_notification_task
logger = get_app_logger("events_handlers")


def handle_order_created(event_type: str, event_format: str, payload: dict, metadata: dict):
    logger.info(f"Handling {event_type} [{event_format}] payload={payload} metadata={metadata}")
    
    # Optimize: Directly send WhatsApp notification if enabled (no separate task/handler)
    if getattr(settings, 'WHATSAPP_ENABLED', False):
        try:
            # Queue WhatsApp notification asynchronously for optimal performance
            send_whatsapp_event_notification_task.apply_async(
                kwargs={
                    'event_type': event_type,
                    'payload': payload,
                    'metadata': metadata or {}
                }
            )
            logger.info(f"WhatsApp notification queued for order {payload.get('order_id', 'unknown')}")
        except Exception as e:
            logger.error(f"Failed to queue WhatsApp notification: {str(e)}")
    
    return {"ok": True}


def handle_order_invoiced(event_type: str, event_format: str, payload: dict, metadata: dict):
    logger.info(f"Handling {event_type} [{event_format}] payload={payload} metadata={metadata}")
    return {"ok": True}


def handle_cn_accepted(event_type: str, event_format: str, payload: dict, metadata: dict):
    logger.info(f"Handling {event_type} [{event_format}] payload={payload} metadata={metadata}")
    return {"ok": True}


def handle_cn_delivered(event_type: str, event_format: str, payload: dict, metadata: dict):
    logger.info(f"Handling {event_type} [{event_format}] payload={payload} metadata={metadata}")
    return {"ok": True}


def handle_order_delivered(event_type: str, event_format: str, payload: dict, metadata: dict):
    logger.info(f"Handling {event_type} [{event_format}] payload={payload} metadata={metadata}")
    return {"ok": True}


def handle_pickup_completed(event_type: str, event_format: str, payload: dict, metadata: dict):
    logger.info(f"Handling {event_type} [{event_format}] payload={payload} metadata={metadata}")
    return {"ok": True}
