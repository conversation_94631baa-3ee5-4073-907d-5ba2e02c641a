# Rozana OMS API - Quick Start Guide

## Prerequisites
- <PERSON><PERSON> and <PERSON><PERSON> Compose installed
- Postman installed
- API server running (use `docker compose up -d`)

## Quick Setup

### 1. Start the API Server
```bash
cd /path/to/rozana-oms-service
docker compose up -d
```

### 2. Verify API is Running
```bash
curl http://localhost:8000/health
# Should return: {"status":"healthy","version":"4.0.0","service":"rozana-oms"}
```

### 3. Import Postman Files
1. Open Postman
2. Import `Rozana_OMS_Complete.postman_collection.json`
3. Import `Rozana_OMS_Environment.postman_environment.json`
4. Select the "Rozana OMS - Environment Variables" environment

## Authentication Quick Start

### For Mobile App Testing:
1. **Run "Firebase App Login":**
   - Update email/password in request body
   - Send request
   - Copy `idToken` from response
   - Set it as `app_firebase_token` in environment variables

2. **Test App Endpoints:**
   - All `/app/v1/*` endpoints now work with Bearer token

### For POS System Testing:
1. **Run "Firebase POS Login":**
   - Credentials are pre-configured
   - Send request
   - Copy `idToken` from response  
   - Set it as `pos_firebase_token` in environment variables

2. **Test POS Endpoints:**
   - All `/pos/v1/*` endpoints now work with Bearer token

### For External API Testing:
1. **Set API Token:**
   - Set `api_token` to any test value (e.g., "test_token_123")
   - All `/api/v1/*` endpoints use this token without "Bearer" prefix

## Sample Testing Flow

### 1. Create an Order (Mobile App)
```
POST /app/v1/create_order
- Authentication: Bearer {{app_firebase_token}}
- Use the pre-configured request body
- Note the order_id from response
```

### 2. Check Order Details
```
GET /app/v1/order_details?order_id={{order_id}}
- Use the order_id from step 1
```

### 3. Create Payment Order
```
POST /app/v1/create_payment_order
- Use the order_id from step 1
- Note the razorpay_order_id from response
```

### 4. List Orders
```
GET /app/v1/orders
- See all orders for the authenticated user
```

### 5. Test Order Updates
```
PUT /app/v1/update_order_status
- Change order status to "confirmed"
```

## Common Request Bodies

### Create Order (App/POS):
```json
{
  "customer_id": "test_customer_123",
  "customer_name": "John Doe",
  "facility_id": "test_facility_123", 
  "facility_name": "Rozana Test Store",
  "total_amount": 150.50,
  "items": [
    {
      "sku": "ITEM001",
      "name": "Sample Product",
      "quantity": 2,
      "unit_price": 75.25,
      "sale_price": 75.25
    }
  ],
  "address": {
    "full_name": "John Doe",
    "phone_number": "+91-9876543210",
    "address_line1": "123 Main Street",
    "city": "Mumbai",
    "state": "Maharashtra", 
    "postal_code": "400001",
    "country": "india",
    "type_of_address": "home"
  },
  "payment": [
    {
      "payment_mode": "razorpay",
      "create_payment_order": true,
      "amount": 150.50
    }
  ]
}
```

## Environment Variables Reference

| Variable | Description | Example |
|----------|-------------|---------|
| `base_url` | API base URL | `http://localhost:8000` |
| `app_firebase_token` | Mobile app auth token | `eyJhbGciOiJSUzI1Ni...` |
| `pos_firebase_token` | POS system auth token | `eyJhbGciOiJSUzI1Ni...` |
| `api_token` | External API token | `test_token_123` |
| `customer_id` | Test customer ID | `test_customer_123` |
| `facility_id` | Test facility ID | `test_facility_123` |
| `order_id` | Current order ID | Auto-populated |

## Endpoint Categories

### 🔓 **No Authentication Required:**
- `GET /health` - Health check

### 🔒 **Firebase App Authentication:**
- All `/app/v1/*` endpoints (14 endpoints)
- Format: `Authorization: Bearer {{app_firebase_token}}`

### 🔒 **Firebase POS Authentication:**  
- All `/pos/v1/*` endpoints (5 endpoints)
- Format: `Authorization: Bearer {{pos_firebase_token}}`

### 🔒 **Custom Token Authentication:**
- All `/api/v1/*` endpoints (5 endpoints)  
- Format: `Authorization: {{api_token}}` (no Bearer prefix)

### 🔒 **Webhook Signature:**
- `/webhooks/v1/razorpay_webhook`
- Header: `X-Razorpay-Signature: signature`

## Troubleshooting

### Common Issues:

1. **401 Unauthorized:**
   - Check if Firebase token is set correctly
   - Verify token hasn't expired
   - Ensure Bearer prefix for app/pos endpoints

2. **"Field required" for API endpoints:**
   - Missing Authorization header
   - Add `Authorization: {{api_token}}` header

3. **"Invalid token":**
   - Token format incorrect
   - For API endpoints, don't use "Bearer" prefix

4. **Connection refused:**
   - API server not running
   - Check with `docker compose ps`
   - Start with `docker compose up -d`

### Validation Commands:
```bash
# Check API health
curl http://localhost:8000/health

# Run comprehensive tests  
./test_api_endpoints.sh

# Check all endpoints
curl -s http://localhost:8000/openapi.json | jq '.paths | keys'
```

## Next Steps

1. **Customize Authentication:** Update Firebase credentials for your environment
2. **Test Payment Flow:** Configure Razorpay credentials for payment testing  
3. **Integration Testing:** Use the collection for integration testing
4. **Documentation:** Reference the full API_ANALYSIS_REPORT.md for detailed documentation

## Support

- **API Documentation:** http://localhost:8000/docs
- **OpenAPI Schema:** http://localhost:8000/openapi.json
- **Collection Files:** Import the provided `.json` files
- **Test Script:** Run `./test_api_endpoints.sh` for validation
