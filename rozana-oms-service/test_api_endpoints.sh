#!/bin/bash

# Rozana OMS API Test Script
# This script tests all available endpoints to validate the Postman collection

BASE_URL="http://localhost:8000"
echo "Testing Rozana OMS API at $BASE_URL"
echo "=================================================="

# Test 1: Health Check (No auth required)
echo "1. Testing Health Check..."
HEALTH_RESPONSE=$(curl -s "$BASE_URL/health")
echo "Response: $HEALTH_RESPONSE"
if echo "$HEALTH_RESPONSE" | grep -q "healthy"; then
    echo "✅ Health check passed"
else
    echo "❌ Health check failed"
fi
echo ""

# Test 2: API Documentation (No auth required)
echo "2. Testing API Documentation..."
DOCS_RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" "$BASE_URL/docs")
if [ "$DOCS_RESPONSE" == "200" ]; then
    echo "✅ API documentation accessible"
else
    echo "❌ API documentation not accessible (HTTP $DOCS_RESPONSE)"
fi
echo ""

# Test 3: OpenAPI Schema (No auth required)
echo "3. Testing OpenAPI Schema..."
OPENAPI_RESPONSE=$(curl -s "$BASE_URL/openapi.json" | jq -r '.info.title' 2>/dev/null)
if [ "$OPENAPI_RESPONSE" == "Rozana OMS" ]; then
    echo "✅ OpenAPI schema accessible"
else
    echo "❌ OpenAPI schema not accessible"
fi
echo ""

# Test 4: App endpoints without authentication (Should fail)
echo "4. Testing App endpoints without authentication..."
APP_RESPONSE=$(curl -s -X POST "$BASE_URL/app/v1/create_order" -H "Content-Type: application/json" -d '{}')
if echo "$APP_RESPONSE" | grep -q "Unauthorized"; then
    echo "✅ App endpoints properly protected with authentication"
else
    echo "❌ App endpoints authentication check failed"
    echo "Response: $APP_RESPONSE"
fi
echo ""

# Test 5: POS endpoints without authentication (Should fail)
echo "5. Testing POS endpoints without authentication..."
POS_RESPONSE=$(curl -s -X POST "$BASE_URL/pos/v1/create_order" -H "Content-Type: application/json" -d '{}')
if echo "$POS_RESPONSE" | grep -q "Unauthorized"; then
    echo "✅ POS endpoints properly protected with authentication"
else
    echo "❌ POS endpoints authentication check failed"
    echo "Response: $POS_RESPONSE"
fi
echo ""

# Test 6: API endpoints without authorization header (Should fail)
echo "6. Testing API endpoints without authorization header..."
API_RESPONSE=$(curl -s -X GET "$BASE_URL/api/v1/get_orders?customer_id=test123")
if echo "$API_RESPONSE" | grep -q "Field required\|authorization"; then
    echo "✅ API endpoints properly protected with authorization header"
else
    echo "❌ API endpoints authorization check failed"
    echo "Response: $API_RESPONSE"
fi
echo ""

# Test 7: API endpoints with invalid token
echo "7. Testing API endpoints with invalid authorization token..."
API_INVALID_RESPONSE=$(curl -s -X GET "$BASE_URL/api/v1/get_orders?customer_id=test123" -H "Authorization: invalid_token")
if echo "$API_INVALID_RESPONSE" | grep -q "Invalid token"; then
    echo "✅ API endpoints properly validate authorization tokens"
else
    echo "❌ API endpoints token validation failed"
    echo "Response: $API_INVALID_RESPONSE"
fi
echo ""

# Test 8: Webhook endpoint (Should accept POST)
echo "8. Testing Webhook endpoint..."
WEBHOOK_RESPONSE=$(curl -s -X POST "$BASE_URL/webhooks/v1/razorpay_webhook" \
    -H "Content-Type: application/json" \
    -H "X-Razorpay-Signature: test_signature" \
    -d '{"event": "payment.captured", "payload": {}}')

# Webhook should respond but may fail due to invalid signature
if echo "$WEBHOOK_RESPONSE" | grep -q -E "Invalid signature|status.*ok"; then
    echo "✅ Webhook endpoint accessible"
else
    echo "❌ Webhook endpoint not accessible"
    echo "Response: $WEBHOOK_RESPONSE"
fi
echo ""

# Test 9: Check all endpoints from OpenAPI schema
echo "9. Checking all endpoints from OpenAPI schema..."
ENDPOINTS=$(curl -s "$BASE_URL/openapi.json" | jq -r '.paths | keys[]' 2>/dev/null)
ENDPOINT_COUNT=$(echo "$ENDPOINTS" | wc -l)
echo "Total endpoints discovered: $ENDPOINT_COUNT"
echo "Endpoints:"
echo "$ENDPOINTS" | sed 's/^/  - /'
echo ""

# Test 10: Check endpoint methods and authentication requirements
echo "10. Analyzing endpoint authentication requirements..."
curl -s "$BASE_URL/openapi.json" | jq -r '
.paths | to_entries[] | 
"\(.key): " + (
  .value | to_entries[] | 
  "\(.key | ascii_upcase) - " + (
    if .value.security then 
      "🔒 Requires authentication" 
    else 
      "🔓 No authentication required" 
    end
  )
)' 2>/dev/null | head -20
echo ""

echo "=================================================="
echo "API Testing Complete"
echo ""
echo "Summary:"
echo "- Total endpoints: $ENDPOINT_COUNT"
echo "- Authentication: Firebase tokens for app/pos endpoints"
echo "- Authorization: Custom tokens for api endpoints"
echo "- Webhooks: Available for Razorpay integration"
echo ""
echo "To use the Postman collection:"
echo "1. Import Rozana_OMS_Complete.postman_collection.json"
echo "2. Import Rozana_OMS_Environment.postman_environment.json"
echo "3. Get Firebase tokens using the Authentication endpoints"
echo "4. Set the tokens in environment variables"
echo "5. Test all endpoints with proper authentication"
