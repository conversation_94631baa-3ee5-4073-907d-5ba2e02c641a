{"info": {"_postman_id": "comprehensive-oms-api-collection", "name": "Rozana OMS - Complete API Collection", "description": "Comprehensive API collection for Rozana Order Management System (OMS) v4.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "oms-complete-api"}, "variable": [{"key": "base_url", "value": "http://localhost:8000", "type": "string"}, {"key": "app_firebase_token", "value": "", "type": "string", "description": "Firebase token for mobile app authentication"}, {"key": "pos_firebase_token", "value": "", "type": "string", "description": "Firebase token for POS system authentication"}, {"key": "api_token", "value": "", "type": "string", "description": "API token for external integrations"}, {"key": "customer_id", "value": "test_customer_123", "type": "string"}, {"key": "facility_id", "value": "test_facility_123", "type": "string"}, {"key": "order_id", "value": "", "type": "string"}], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/health", "host": ["{{base_url}}"], "path": ["health"]}, "description": "Check if the OMS API is healthy and running"}, "response": []}, {"name": "Authentication", "item": [{"name": "Firebase App Login", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"your_password\",\n    \"returnSecureToken\": true\n}"}, "url": {"raw": "https://identitytoolkit.googleapis.com/v1/accounts:signInWithPassword?key=AIzaSyBEEWROSUbSLVl9T3HHSp8VdgsU6oTrdFI", "protocol": "https", "host": ["identitytoolkit", "googlea<PERSON>", "com"], "path": ["v1", "accounts:signInWithPassword"], "query": [{"key": "key", "value": "AIzaSyBEEWROSUbSLVl9T3HHSp8VdgsU6oTrdFI"}]}, "description": "Login to get Firebase token for mobile app endpoints"}, "response": []}, {"name": "Firebase POS Login", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"wZVWpnSIpY\",\n    \"returnSecureToken\": true\n}"}, "url": {"raw": "https://identitytoolkit.googleapis.com/v1/accounts:signInWithPassword?key=AIzaSyC_fE1A0JapukDalgCdJix4mmoVWx-K-2g", "protocol": "https", "host": ["identitytoolkit", "googlea<PERSON>", "com"], "path": ["v1", "accounts:signInWithPassword"], "query": [{"key": "key", "value": "AIzaSyC_fE1A0JapukDalgCdJix4mmoVWx-K-2g"}]}, "description": "Login to get Firebase token for POS system endpoints"}, "response": []}, {"name": "Firebase Token Refresh", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "grant_type", "value": "refresh_token", "type": "text"}, {"key": "refresh_token", "value": "your_refresh_token_here", "type": "text"}]}, "url": {"raw": "https://securetoken.googleapis.com/v1/token?key=AIzaSyBEEWROSUbSLVl9T3HHSp8VdgsU6oTrdFI", "protocol": "https", "host": ["securetoken", "googlea<PERSON>", "com"], "path": ["v1", "token"], "query": [{"key": "key", "value": "AIzaSyBEEWROSUbSLVl9T3HHSp8VdgsU6oTrdFI"}]}, "description": "Refresh expired Firebase token"}, "response": []}], "description": "Authentication endpoints for Firebase tokens"}, {"name": "Mobile App Endpoints", "item": [{"name": "Orders", "item": [{"name": "Create Order", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{app_firebase_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"customer_id\": \"{{customer_id}}\",\n  \"customer_name\": \"<PERSON>\",\n  \"facility_id\": \"{{facility_id}}\",\n  \"facility_name\": \"Rozana Store Mumbai\",\n  \"status\": \"pending\",\n  \"total_amount\": 150.50,\n  \"is_approved\": true,\n  \"items\": [\n    {\n      \"sku\": \"ITEM001\",\n      \"name\": \"Sample Product 1\",\n      \"quantity\": 2,\n      \"unit_price\": 50.25,\n      \"sale_price\": 50.25\n    },\n    {\n      \"sku\": \"ITEM002\",\n      \"name\": \"Sample Product 2\",\n      \"quantity\": 1,\n      \"unit_price\": 50.0,\n      \"sale_price\": 50.0\n    }\n  ],\n  \"address\": {\n    \"full_name\": \"<PERSON>\",\n    \"phone_number\": \"+91-9876543210\",\n    \"address_line1\": \"123 Main Street\",\n    \"address_line2\": \"Apt 4B\",\n    \"city\": \"Mumbai\",\n    \"state\": \"Maharashtra\",\n    \"postal_code\": \"400001\",\n    \"country\": \"india\",\n    \"type_of_address\": \"home\",\n    \"longitude\": 72.8777,\n    \"latitude\": 19.0760\n  },\n  \"payment\": [\n    {\n      \"payment_mode\": \"razorpay\",\n      \"create_payment_order\": true,\n      \"amount\": 150.50\n    }\n  ],\n  \"customer_email\": \"<EMAIL>\",\n  \"customer_phone\": \"+91-9876543210\"\n}"}, "url": {"raw": "{{base_url}}/app/v1/create_order", "host": ["{{base_url}}"], "path": ["app", "v1", "create_order"]}, "description": "Create a new order via mobile app"}, "response": []}, {"name": "Get Order Details", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{app_firebase_token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/app/v1/order_details?order_id={{order_id}}", "host": ["{{base_url}}"], "path": ["app", "v1", "order_details"], "query": [{"key": "order_id", "value": "{{order_id}}"}]}, "description": "Get details of a specific order"}, "response": []}, {"name": "Get All Orders", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{app_firebase_token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/app/v1/orders?limit=20&offset=0&sort_order=desc", "host": ["{{base_url}}"], "path": ["app", "v1", "orders"], "query": [{"key": "limit", "value": "20"}, {"key": "offset", "value": "0"}, {"key": "sort_order", "value": "desc"}]}, "description": "List orders for logged-in mobile user"}, "response": []}, {"name": "Order Again", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{app_firebase_token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/app/v1/order_again?limit=20&offset=0", "host": ["{{base_url}}"], "path": ["app", "v1", "order_again"], "query": [{"key": "limit", "value": "20"}, {"key": "offset", "value": "0"}]}, "description": "Get products from previous orders for easy reordering"}, "response": []}, {"name": "Update Order Status (PUT)", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{app_firebase_token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"order_id\": \"{{order_id}}\",\n  \"status\": \"confirmed\"\n}"}, "url": {"raw": "{{base_url}}/app/v1/update_order_status", "host": ["{{base_url}}"], "path": ["app", "v1", "update_order_status"]}, "description": "Update order status (mobile app) using PUT method"}, "response": []}, {"name": "Update Order Status (PATCH)", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{app_firebase_token}}", "type": "string"}]}, "method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"order_id\": \"{{order_id}}\",\n  \"status\": \"delivered\"\n}"}, "url": {"raw": "{{base_url}}/app/v1/update_order_status", "host": ["{{base_url}}"], "path": ["app", "v1", "update_order_status"]}, "description": "Update order status (mobile app) using PATCH method"}, "response": []}, {"name": "Update Item Status (PUT)", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{app_firebase_token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"order_id\": \"{{order_id}}\",\n  \"sku\": \"ITEM001\",\n  \"status\": \"confirmed\"\n}"}, "url": {"raw": "{{base_url}}/app/v1/update_item_status", "host": ["{{base_url}}"], "path": ["app", "v1", "update_item_status"]}, "description": "Update individual item status (mobile app) using PUT method"}, "response": []}, {"name": "Update Item Status (PATCH)", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{app_firebase_token}}", "type": "string"}]}, "method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"order_id\": \"{{order_id}}\",\n  \"sku\": \"ITEM001\",\n  \"status\": \"delivered\"\n}"}, "url": {"raw": "{{base_url}}/app/v1/update_item_status", "host": ["{{base_url}}"], "path": ["app", "v1", "update_item_status"]}, "description": "Update individual item status (mobile app) using PATCH method"}, "response": []}, {"name": "Cancel Order", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{app_firebase_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"order_id\": \"{{order_id}}\"\n}"}, "url": {"raw": "{{base_url}}/app/v1/cancel_order", "host": ["{{base_url}}"], "path": ["app", "v1", "cancel_order"]}, "description": "Cancel an order"}, "response": []}], "description": "Order management endpoints for mobile app"}, {"name": "Returns", "item": [{"name": "Create Return - Partial", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{app_firebase_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"order_id\": \"{{order_id}}\",\n  \"items\": [\n    {\n      \"sku\": \"ITEM001\",\n      \"quantity\": 1\n    }\n  ],\n  \"return_reason\": \"Product damaged\"\n}"}, "url": {"raw": "{{base_url}}/app/v1/create_return", "host": ["{{base_url}}"], "path": ["app", "v1", "create_return"]}, "description": "Create a partial return for specific items"}, "response": []}, {"name": "Create Return - Full Order", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{app_firebase_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"order_id\": \"{{order_id}}\",\n  \"order_full_return\": true,\n  \"return_reason\": \"Order not needed\"\n}"}, "url": {"raw": "{{base_url}}/app/v1/create_return", "host": ["{{base_url}}"], "path": ["app", "v1", "create_return"]}, "description": "Create a full order return"}, "response": []}], "description": "Return management endpoints for mobile app"}, {"name": "Payments", "item": [{"name": "Create Payment Order", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{app_firebase_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"order_id\": \"{{order_id}}\",\n  \"amount\": 150.50,\n  \"customer_id\": \"{{customer_id}}\",\n  \"customer_name\": \"<PERSON>\",\n  \"customer_email\": \"<EMAIL>\",\n  \"customer_phone\": \"+91-9876543210\",\n  \"notes\": {\n    \"order_type\": \"grocery\",\n    \"customer_notes\": \"Handle with care\"\n  }\n}"}, "url": {"raw": "{{base_url}}/app/v1/create_payment_order", "host": ["{{base_url}}"], "path": ["app", "v1", "create_payment_order"]}, "description": "Create a Razorpay payment order for an existing OMS order"}, "response": []}, {"name": "Verify Payment", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{app_firebase_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"razorpay_order_id\": \"order_xyz123\",\n  \"razorpay_payment_id\": \"pay_abc456\",\n  \"razorpay_signature\": \"signature_hash\",\n  \"oms_order_id\": \"{{order_id}}\"\n}"}, "url": {"raw": "{{base_url}}/app/v1/verify_payment", "host": ["{{base_url}}"], "path": ["app", "v1", "verify_payment"]}, "description": "Verify payment signature and update order status"}, "response": []}, {"name": "Get Payment Status", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{app_firebase_token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/app/v1/payment_status/{{order_id}}", "host": ["{{base_url}}"], "path": ["app", "v1", "payment_status", "{{order_id}}"]}, "description": "Get payment status for an order"}, "response": []}], "description": "Payment management endpoints for mobile app"}, {"name": "Encryption", "item": [{"name": "Encrypt Customer Code", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{app_firebase_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"customer_code\": \"CUST12345\"\n}"}, "url": {"raw": "{{base_url}}/app/v1/encrypt_customer_code", "host": ["{{base_url}}"], "path": ["app", "v1", "encrypt_customer_code"]}, "description": "Encrypt customer code using AES encryption"}, "response": []}], "description": "Encryption utility endpoints"}], "description": "All endpoints for mobile app with Firebase authentication"}, {"name": "POS System Endpoints", "item": [{"name": "Create Order", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{pos_firebase_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"customer_id\": \"{{customer_id}}\",\n  \"customer_name\": \"<PERSON>\",\n  \"facility_id\": \"{{facility_id}}\",\n  \"facility_name\": \"Rozana Store Delhi\",\n  \"status\": \"confirmed\",\n  \"total_amount\": 200.75,\n  \"is_approved\": true,\n  \"items\": [\n    {\n      \"sku\": \"POS001\",\n      \"name\": \"POS Product 1\",\n      \"quantity\": 3,\n      \"unit_price\": 66.92,\n      \"sale_price\": 66.92\n    }\n  ],\n  \"address\": {\n    \"full_name\": \"<PERSON>\",\n    \"phone_number\": \"+91-9123456789\",\n    \"address_line1\": \"456 Store Street\",\n    \"city\": \"Delhi\",\n    \"state\": \"Delhi\",\n    \"postal_code\": \"110001\",\n    \"country\": \"india\",\n    \"type_of_address\": \"work\"\n  },\n  \"payment\": [\n    {\n      \"payment_mode\": \"cash\",\n      \"create_payment_order\": false,\n      \"amount\": 200.75\n    }\n  ]\n}"}, "url": {"raw": "{{base_url}}/pos/v1/create_order", "host": ["{{base_url}}"], "path": ["pos", "v1", "create_order"]}, "description": "Create a new order via POS system"}, "response": []}, {"name": "Get Order Details", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{pos_firebase_token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/pos/v1/order_details?order_id={{order_id}}", "host": ["{{base_url}}"], "path": ["pos", "v1", "order_details"], "query": [{"key": "order_id", "value": "{{order_id}}"}]}, "description": "Get details of a specific order via POS"}, "response": []}, {"name": "Get All Facility Orders", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{pos_firebase_token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/pos/v1/orders?facility_name=Rozana Store Delhi&limit=20&offset=0&sort_order=desc", "host": ["{{base_url}}"], "path": ["pos", "v1", "orders"], "query": [{"key": "facility_name", "value": "Rozana Store Delhi"}, {"key": "limit", "value": "20"}, {"key": "offset", "value": "0"}, {"key": "sort_order", "value": "desc"}]}, "description": "List orders for a specific facility"}, "response": []}, {"name": "Update Order Status (PUT)", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{pos_firebase_token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"order_id\": \"{{order_id}}\",\n  \"status\": \"delivered\"\n}"}, "url": {"raw": "{{base_url}}/pos/v1/update_order_status", "host": ["{{base_url}}"], "path": ["pos", "v1", "update_order_status"]}, "description": "Update order status via POS using PUT method"}, "response": []}, {"name": "Update Order Status (PATCH)", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{pos_firebase_token}}", "type": "string"}]}, "method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"order_id\": \"{{order_id}}\",\n  \"status\": \"confirmed\"\n}"}, "url": {"raw": "{{base_url}}/pos/v1/update_order_status", "host": ["{{base_url}}"], "path": ["pos", "v1", "update_order_status"]}, "description": "Update order status via POS using PATCH method"}, "response": []}, {"name": "Update Item Status (PUT)", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{pos_firebase_token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"order_id\": \"{{order_id}}\",\n  \"sku\": \"POS001\",\n  \"status\": \"delivered\"\n}"}, "url": {"raw": "{{base_url}}/pos/v1/update_item_status", "host": ["{{base_url}}"], "path": ["pos", "v1", "update_item_status"]}, "description": "Update individual item status via POS using PUT method"}, "response": []}, {"name": "Update Item Status (PATCH)", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{pos_firebase_token}}", "type": "string"}]}, "method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"order_id\": \"{{order_id}}\",\n  \"sku\": \"POS001\",\n  \"status\": \"confirmed\"\n}"}, "url": {"raw": "{{base_url}}/pos/v1/update_item_status", "host": ["{{base_url}}"], "path": ["pos", "v1", "update_item_status"]}, "description": "Update individual item status via POS using PATCH method"}, "response": []}], "description": "All endpoints for POS system with Firebase authentication"}, {"name": "External API Endpoints", "item": [{"name": "Get Orders", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{api_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/v1/get_orders?customer_id={{customer_id}}&limit=20&offset=0&sort_order=desc", "host": ["{{base_url}}"], "path": ["api", "v1", "get_orders"], "query": [{"key": "customer_id", "value": "{{customer_id}}"}, {"key": "limit", "value": "20"}, {"key": "offset", "value": "0"}, {"key": "sort_order", "value": "desc"}]}, "description": "Get orders for a customer with token validation"}, "response": []}, {"name": "Get Order Items", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{api_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/v1/order_items?customer_id={{customer_id}}&order_id={{order_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "order_items"], "query": [{"key": "customer_id", "value": "{{customer_id}}"}, {"key": "order_id", "value": "{{order_id}}"}]}, "description": "Get order items for a specific order with token validation"}, "response": []}, {"name": "Cancel Order", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{api_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"customer_id\": \"{{customer_id}}\",\n  \"order_id\": \"{{order_id}}\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/cancel_order", "host": ["{{base_url}}"], "path": ["api", "v1", "cancel_order"]}, "description": "Cancel an order with token validation"}, "response": []}, {"name": "Return Items - Partial", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{api_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"customer_id\": \"{{customer_id}}\",\n  \"order_id\": \"{{order_id}}\",\n  \"items\": [\n    {\n      \"sku\": \"ITEM001\",\n      \"quantity\": 1\n    }\n  ]\n}"}, "url": {"raw": "{{base_url}}/api/v1/return_items", "host": ["{{base_url}}"], "path": ["api", "v1", "return_items"]}, "description": "Return specific items from an order with token validation"}, "response": []}, {"name": "Return Full Order", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{api_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"customer_id\": \"{{customer_id}}\",\n  \"order_id\": \"{{order_id}}\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/return_full_order", "host": ["{{base_url}}"], "path": ["api", "v1", "return_full_order"]}, "description": "Return all items in an order with token validation"}, "response": []}], "description": "External API endpoints with token-based authentication (no Bearer prefix)"}, {"name": "Webhooks", "item": [{"name": "Razorpay Webhook", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Razorpay-Signature", "value": "webhook_signature_here", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"entity\": \"event\",\n  \"account_id\": \"acc_test_account\",\n  \"event\": \"payment.captured\",\n  \"contains\": [\"payment\"],\n  \"payload\": {\n    \"payment\": {\n      \"entity\": {\n        \"id\": \"pay_test_payment_id\",\n        \"entity\": \"payment\",\n        \"amount\": 15050,\n        \"currency\": \"INR\",\n        \"status\": \"captured\",\n        \"order_id\": \"order_test_order_id\",\n        \"invoice_id\": null,\n        \"international\": false,\n        \"method\": \"card\",\n        \"amount_refunded\": 0,\n        \"refund_status\": null,\n        \"captured\": true,\n        \"description\": \"Payment for order\",\n        \"card_id\": \"card_test_card_id\",\n        \"bank\": null,\n        \"wallet\": null,\n        \"vpa\": null,\n        \"email\": \"<EMAIL>\",\n        \"contact\": \"+************\",\n        \"notes\": {\n          \"oms_order_id\": \"{{order_id}}\",\n          \"customer_id\": \"{{customer_id}}\"\n        },\n        \"fee\": 354,\n        \"tax\": 54,\n        \"error_code\": null,\n        \"error_description\": null,\n        \"error_source\": null,\n        \"error_step\": null,\n        \"error_reason\": null,\n        \"acquirer_data\": {\n          \"auth_code\": \"123456\"\n        },\n        \"created_at\": **********\n      }\n    }\n  },\n  \"created_at\": **********\n}"}, "url": {"raw": "{{base_url}}/webhooks/v1/razorpay_webhook", "host": ["{{base_url}}"], "path": ["webhooks", "v1", "razorpay_webhook"]}, "description": "Webhook endpoint for Razorpay payment status updates"}, "response": []}], "description": "Webhook endpoints for external service notifications"}]}